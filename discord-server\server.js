const express = require('express');
const cors = require('cors');
const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const glob = require('glob');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Discord Bot Setup
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

let discordChannel = null;

// Конфигурация
const config = {
    discordToken: process.env.DISCORD_BOT_TOKEN,
    channelId: process.env.DISCORD_CHANNEL_ID,
    wowSavedVariablesPath: process.env.WOW_SAVEDVARIABLES_PATH || 'C:\\Program Files (x86)\\World of Warcraft\\_retail_\\WTF\\Account\\*\\SavedVariables\\WoWToDiscord.lua',
    pollInterval: parseInt(process.env.POLL_INTERVAL) || 3000, // миллисекунды
    messageFormat: {
        embedColor: 0x0099ff,
        showRealm: true,
        showTimestamp: true
    }
};

// Инициализация Discord бота
client.once('ready', () => {
    console.log(`✅ Discord bot logged in as ${client.user.tag}`);

    // Получаем канал для отправки сообщений
    if (config.channelId) {
        discordChannel = client.channels.cache.get(config.channelId);
        if (discordChannel) {
            console.log(`✅ Connected to Discord channel: ${discordChannel.name}`);
        } else {
            console.error(`❌ Could not find Discord channel with ID: ${config.channelId}`);
        }
    }
});

client.on('error', (error) => {
    console.error('❌ Discord client error:', error);
});

// Функция для отправки сообщения в Discord
async function sendToDiscord(messageData) {
    if (!discordChannel) {
        console.error('❌ Discord channel not available');
        return false;
    }

    try {
        // Создаем красивый embed для сообщения
        const embed = new EmbedBuilder()
            .setColor(config.messageFormat.embedColor)
            .setTitle(`💬 ${getChannelDisplayName(messageData.channel)}`)
            .setDescription(`**${messageData.sender}**: ${messageData.message}`)
            .addFields(
                { name: '📍 Channel', value: messageData.channel, inline: true }
            );

        if (config.messageFormat.showRealm && messageData.realm) {
            embed.addFields({ name: '🌍 Realm', value: messageData.realm, inline: true });
        }

        if (config.messageFormat.showTimestamp && messageData.timestamp) {
            embed.setTimestamp(new Date(messageData.timestamp));
        }

        await discordChannel.send({ embeds: [embed] });
        console.log(`✅ Message sent to Discord: ${messageData.sender} (${messageData.channel})`);
        return true;
    } catch (error) {
        console.error('❌ Error sending message to Discord:', error);
        return false;
    }
}

// Функция для получения читаемого названия канала
function getChannelDisplayName(channel) {
    const channelNames = {
        'SAY': 'Say',
        'YELL': 'Yell',
        'WHISPER': 'Whisper',
        'PARTY': 'Party',
        'RAID': 'Raid',
        'GUILD': 'Guild',
        'OFFICER': 'Officer',
        'CHANNEL': 'Channel',
        'EMOTE': 'Emote',
        'TEXT_EMOTE': 'Text Emote',
        'TEST': 'Test Message'
    };
    return channelNames[channel] || channel;
}

// Переменные для отслеживания обработанных сообщений
let processedMessageIds = new Set();
let lastProcessedTime = Date.now();

// Функция для поиска SavedVariables файлов
function findSavedVariablesFiles() {
    try {
        const pattern = config.wowSavedVariablesPath;
        const files = glob.sync(pattern);
        return files.filter(file => fs.existsSync(file));
    } catch (error) {
        console.error('❌ Error finding SavedVariables files:', error);
        return [];
    }
}

// Функция для парсинга Lua файла SavedVariables
function parseLuaSavedVariables(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');

        // Ищем WoWToDiscordDB в файле
        const dbMatch = content.match(/WoWToDiscordDB\s*=\s*({[\s\S]*?})\s*$/m);
        if (!dbMatch) {
            return null;
        }

        // Простой парсер Lua таблиц в JSON
        let luaTable = dbMatch[1];

        // Заменяем Lua синтаксис на JSON
        luaTable = luaTable
            .replace(/\[(\d+)\]\s*=/g, '"$1":')  // [1] = -> "1":
            .replace(/\["([^"]+)"\]\s*=/g, '"$1":')  // ["key"] = -> "key":
            .replace(/(\w+)\s*=/g, '"$1":')  // key = -> "key":
            .replace(/,\s*}/g, '}')  // убираем запятые перед }
            .replace(/,\s*]/g, ']'); // убираем запятые перед ]

        return JSON.parse(luaTable);
    } catch (error) {
        console.error(`❌ Error parsing SavedVariables file ${filePath}:`, error);
        return null;
    }
}

// Функция для обработки сообщений из SavedVariables
async function processSavedVariablesMessages() {
    const files = findSavedVariablesFiles();

    if (files.length === 0) {
        return;
    }

    for (const file of files) {
        try {
            const data = parseLuaSavedVariables(file);
            if (!data || !data.messageQueue) {
                continue;
            }

            // Обрабатываем новые сообщения
            const newMessages = data.messageQueue.filter(msg =>
                !msg.processed &&
                !processedMessageIds.has(msg.id) &&
                msg.timestamp * 1000 > lastProcessedTime
            );

            for (const message of newMessages) {
                await sendToDiscord({
                    message: message.message,
                    sender: message.sender,
                    channel: message.channel,
                    timestamp: new Date(message.timestamp * 1000).toISOString(),
                    realm: message.realm
                });

                processedMessageIds.add(message.id);
            }

            if (newMessages.length > 0) {
                console.log(`✅ Processed ${newMessages.length} new messages from ${path.basename(file)}`);
            }

        } catch (error) {
            console.error(`❌ Error processing file ${file}:`, error);
        }
    }
}

// HTTP endpoint для получения сообщений от WoW аддона
app.post('/message', async (req, res) => {
    try {
        const { message, sender, channel, timestamp, realm } = req.body;

        // Валидация данных
        if (!message || !sender || !channel) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: message, sender, channel'
            });
        }

        console.log(`📨 Received message from WoW: ${sender} (${channel}): ${message}`);

        // Отправляем в Discord
        const success = await sendToDiscord({
            message,
            sender,
            channel,
            timestamp: timestamp || new Date().toISOString(),
            realm: realm || 'Unknown'
        });

        res.json({
            success: success,
            message: success ? 'Message sent to Discord' : 'Failed to send message to Discord'
        });

    } catch (error) {
        console.error('❌ Error processing message:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
});

// Endpoint для проверки статуса
app.get('/status', (req, res) => {
    const savedVariablesFiles = findSavedVariablesFiles();

    res.json({
        status: 'running',
        discord: {
            connected: client.isReady(),
            channel: discordChannel ? discordChannel.name : 'Not connected'
        },
        savedVariables: {
            path: config.wowSavedVariablesPath,
            filesFound: savedVariablesFiles.length,
            files: savedVariablesFiles.map(f => path.basename(f)),
            pollInterval: config.pollInterval
        },
        processing: {
            processedMessageIds: processedMessageIds.size,
            lastProcessedTime: new Date(lastProcessedTime).toISOString()
        },
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
    });
});

// Endpoint для тестирования
app.post('/test', async (req, res) => {
    const testMessage = {
        message: 'Test message from server',
        sender: 'TestUser',
        channel: 'TEST',
        timestamp: new Date().toISOString(),
        realm: 'TestRealm'
    };

    const success = await sendToDiscord(testMessage);
    res.json({
        success: success,
        message: success ? 'Test message sent' : 'Failed to send test message'
    });
});

// File watcher для fallback метода (если HTTP не работает)
if (config.enableFileWatcher) {
    // Создаем директорию для файлов, если её нет
    if (!fs.existsSync(config.watchDirectory)) {
        fs.mkdirSync(config.watchDirectory, { recursive: true });
    }

    const watcher = chokidar.watch(config.watchDirectory, {
        ignored: /^\./, // игнорируем скрытые файлы
        persistent: true
    });

    watcher.on('add', async (filePath) => {
        if (path.extname(filePath) === '.tmp') {
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                const lines = content.trim().split('\n');

                for (const line of lines) {
                    const [channel, sender, message, timestamp, realm] = line.split('|');

                    if (channel && sender && message) {
                        await sendToDiscord({
                            channel,
                            sender,
                            message,
                            timestamp,
                            realm
                        });
                    }
                }

                // Удаляем обработанный файл
                fs.unlinkSync(filePath);
                console.log(`✅ Processed and deleted file: ${filePath}`);

            } catch (error) {
                console.error(`❌ Error processing file ${filePath}:`, error);
            }
        }
    });

    console.log(`👀 File watcher started for directory: ${config.watchDirectory}`);
}

// Запуск сервера
app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📡 HTTP endpoint: http://localhost:${PORT}/message`);
    console.log(`📊 Status endpoint: http://localhost:${PORT}/status`);
    console.log(`🧪 Test endpoint: http://localhost:${PORT}/test`);
    console.log(`📁 Monitoring SavedVariables: ${config.wowSavedVariablesPath}`);
    console.log(`⏱️ Poll interval: ${config.pollInterval}ms`);

    // Запускаем мониторинг SavedVariables
    startSavedVariablesMonitoring();
});

// Функция для запуска мониторинга SavedVariables
function startSavedVariablesMonitoring() {
    console.log('🔍 Starting SavedVariables monitoring...');

    // Проверяем сразу
    processSavedVariablesMessages();

    // Устанавливаем интервал для регулярной проверки
    setInterval(() => {
        processSavedVariablesMessages();
    }, config.pollInterval);

    console.log('✅ SavedVariables monitoring started');
}

// Подключение к Discord
if (config.discordToken) {
    client.login(config.discordToken).catch(error => {
        console.error('❌ Failed to login to Discord:', error);
        console.log('💡 Make sure DISCORD_BOT_TOKEN is set in .env file');
    });
} else {
    console.error('❌ DISCORD_BOT_TOKEN not found in environment variables');
    console.log('💡 Create a .env file with your Discord bot token');
}

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    client.destroy();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    client.destroy();
    process.exit(0);
});
