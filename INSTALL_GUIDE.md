# 🚀 Пошаговая инструкция установки WoW to Discord

## Шаг 1: Подготовка Discord бота

### 1.1 Создание приложения
1. Откройте [Discord Developer Portal](https://discord.com/developers/applications)
2. Нажмите **"New Application"**
3. Введите название: `WoW Messages Bot`
4. Нажмите **"Create"**

### 1.2 Создание бота
1. В левом меню выберите **"Bot"**
2. Нажмите **"Add Bot"** → **"Yes, do it!"**
3. В разделе **"Token"** нажмите **"Copy"** и сохраните токен в безопасном месте

### 1.3 Настройка разрешений
1. В разделе **"Privileged Gateway Intents"** включите:
   - ✅ Message Content Intent
2. В разделе **"OAuth2"** → **"URL Generator"**:
   - Scope: ✅ `bot`
   - Bot Permissions: 
     - ✅ Send Messages
     - ✅ Use Slash Commands  
     - ✅ Embed Links
     - ✅ Read Message History
3. Скопируйте сгенерированную ссылку и откройте в браузере
4. Выберите сервер и добавьте бота

## Шаг 2: Получение ID канала

1. В Discord включите **Режим разработчика**:
   - Настройки → Расширенные → Режим разработчика ✅
2. Правой кнопкой мыши на канал → **"Копировать ID"**
3. Сохраните ID канала

## Шаг 3: Установка Node.js

1. Скачайте Node.js с [официального сайта](https://nodejs.org/)
2. Установите LTS версию (рекомендуется)
3. Проверьте установку в командной строке:
   ```cmd
   node --version
   npm --version
   ```

## Шаг 4: Установка WoW аддона

1. Найдите папку с аддонами WoW:
   ```
   C:\Program Files (x86)\World of Warcraft\_retail_\Interface\AddOns\
   ```
   
2. Скопируйте папку `WoWToDiscord` в эту директорию

3. Структура должна быть:
   ```
   AddOns\
   └── WoWToDiscord\
       ├── WoWToDiscord.toc
       └── WoWToDiscord.lua
   ```

## Шаг 5: Настройка сервера

1. Откройте командную строку в папке `discord-server`
2. Установите зависимости:
   ```cmd
   npm install
   ```

3. Создайте файл `.env`:
   ```cmd
   copy .env.example .env
   ```

4. Отредактируйте `.env` в блокноте:
   ```env
   DISCORD_BOT_TOKEN=ваш_токен_бота_здесь
   DISCORD_CHANNEL_ID=id_канала_здесь
   WOW_SAVEDVARIABLES_PATH=C:\Program Files (x86)\World of Warcraft\_retail_\WTF\Account\*\SavedVariables\WoWToDiscord.lua
   POLL_INTERVAL=3000
   PORT=3000
   ```

   **Важно:** Если ваш WoW установлен в другом месте, измените путь `WOW_SAVEDVARIABLES_PATH`

## Шаг 6: Первый запуск

1. Запустите сервер:
   ```cmd
   npm start
   ```
   Или дважды кликните на `start.bat`

2. Вы должны увидеть:
   ```
   ✅ Discord bot logged in as WoW Messages Bot#1234
   ✅ Connected to Discord channel: general
   🚀 Server running on port 3000
   📁 Monitoring SavedVariables: C:\Program Files (x86)\World of Warcraft\_retail_\WTF\Account\*\SavedVariables\WoWToDiscord.lua
   🔍 Starting SavedVariables monitoring...
   ✅ SavedVariables monitoring started
   ```

3. Запустите WoW и войдите в игру

4. В игре введите:
   ```
   /wtd status
   ```

5. Протестируйте:
   ```
   /wtd test
   ```

## ✅ Проверка работы

### В игре должно появиться:
```
[WoWToDiscord] v2.0.0 loaded!
[WoWToDiscord] Using file-based message transfer
[WoWToDiscord] Ready to capture messages!
[WoWToDiscord] Status: Enabled
[WoWToDiscord] Queued messages: 0
```

### В Discord должно появиться тестовое сообщение с красивым оформлением

### В консоли сервера:
```
✅ Processed 1 new messages from WoWToDiscord.lua
✅ Message sent to Discord: YourCharacter (TEST)
```

## 🔧 Решение проблем

### Проблема: "Discord bot not connected"
- Проверьте токен бота в `.env`
- Убедитесь, что бот добавлен на сервер
- Проверьте права бота

### Проблема: "Channel not found"
- Проверьте ID канала в `.env`
- Убедитесь, что бот имеет доступ к каналу

### Проблема: "SavedVariables file not found"
- Убедитесь, что путь в `.env` правильный
- Проверьте, что аддон загружен в WoW
- Попробуйте команду `/wtd test` в игре для создания файла

### Проблема: Аддон не загружается
- Проверьте правильность пути к папке AddOns
- Убедитесь, что аддон включен в списке аддонов WoW

## 🎯 Готово!

Теперь все входящие сообщения в WoW будут автоматически пересылаться в Discord!

**Полезные команды:**
- `/wtd toggle` - включить/выключить
- `/wtd status` - проверить статус  
- `/wtd test` - отправить тест
- `http://localhost:3000/status` - статус сервера
