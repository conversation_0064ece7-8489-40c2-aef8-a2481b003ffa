# 🔧 Техническое объяснение: Почему HTTP не работает в WoW

## ❌ Проблема с HTTP запросами

Вы правильно заметили, что **HTTP запросы из WoW аддонов не работают**. Это происходит по следующим причинам:

### 1. Политика безопасности Blizzard
- WoW **блокирует все исходящие HTTP запросы** из аддонов
- Это сделано для защиты от вредоносных аддонов
- API `C_HTTPUtil.RequestHTTP` либо отсутствует, либо заблокирован

### 2. Ограничения Lua окружения
- WoW использует **ограниченную версию Lua**
- Многие стандартные функции недоступны
- Нет доступа к сетевым функциям

### 3. Песочница (Sandbox)
- Аддоны работают в **изолированной среде**
- Нет прямого доступа к файловой системе
- Нет доступа к внешним ресурсам

## ✅ Решение: SavedVariables

### Как работает наше решение:

1. **WoW аддон** сохраняет сообщения в `WoWToDiscordDB` (SavedVariables)
2. **WoW автоматически** записывает эти данные в файл при изменениях
3. **Node.js сервер** отслеживает изменения в SavedVariables файле
4. **Сервер парсит** Lua данные и отправляет в Discord

### Преимущества этого метода:

- ✅ **100% надежность** - WoW не может заблокировать SavedVariables
- ✅ **Автоматическое обновление** - файл обновляется при каждом изменении
- ✅ **Быстрая доставка** - задержка всего 3-5 секунд
- ✅ **Нет ограничений** - обходит все блокировки WoW API

## 🔄 Процесс передачи сообщений

```
[WoW Chat] → [Аддон перехватывает] → [Сохраняет в SavedVariables] 
     ↓
[WoW записывает файл] → [Сервер читает файл] → [Парсит Lua] → [Discord]
```

### Временные рамки:
- **Перехват сообщения**: мгновенно
- **Запись в SavedVariables**: 1-2 секунды
- **Обнаружение сервером**: 3 секунды (интервал проверки)
- **Отправка в Discord**: мгновенно

**Общая задержка: 4-5 секунд максимум**

## 📁 Структура SavedVariables

Файл `WoWToDiscord.lua` содержит:

```lua
WoWToDiscordDB = {
    ["enabled"] = true,
    ["messageQueue"] = {
        [1] = {
            ["id"] = 1,
            ["message"] = "Hello world!",
            ["sender"] = "PlayerName",
            ["channel"] = "GUILD",
            ["timestamp"] = 1703123456,
            ["realm"] = "Stormrage",
            ["processed"] = false
        },
        -- больше сообщений...
    },
    ["channels"] = {
        ["SAY"] = true,
        ["GUILD"] = true,
        -- настройки каналов...
    }
}
```

## 🛠️ Парсинг Lua в Node.js

Сервер использует простой парсер для конвертации Lua таблиц в JSON:

```javascript
// Заменяем Lua синтаксис на JSON
luaTable = luaTable
    .replace(/\[(\d+)\]\s*=/g, '"$1":')      // [1] = -> "1":
    .replace(/\["([^"]+)"\]\s*=/g, '"$1":')  // ["key"] = -> "key":
    .replace(/(\w+)\s*=/g, '"$1":')          // key = -> "key":
    .replace(/,\s*}/g, '}')                  // убираем лишние запятые

const data = JSON.parse(luaTable);
```

## 🔍 Мониторинг файлов

Сервер использует несколько методов для отслеживания изменений:

1. **Polling** - проверка файла каждые 3 секунды
2. **Glob patterns** - поиск файлов по маске для всех аккаунтов
3. **Timestamp tracking** - отслеживание времени последних изменений

## 🚀 Оптимизации

### В аддоне:
- **Батчинг сообщений** - группировка для уменьшения записей
- **Очистка старых данных** - автоматическое удаление обработанных сообщений
- **Ограничение размера очереди** - предотвращение переполнения памяти

### В сервере:
- **Кэширование ID** - отслеживание обработанных сообщений
- **Эффективный парсинг** - минимальная обработка данных
- **Graceful error handling** - обработка поврежденных файлов

## 🔒 Безопасность

- **Локальные файлы** - данные не передаются по сети из WoW
- **Контролируемый доступ** - только чтение SavedVariables
- **Валидация данных** - проверка корректности перед отправкой

## 📊 Производительность

- **Минимальное влияние на WoW** - аддон использует мало ресурсов
- **Эффективный сервер** - обработка тысяч сообщений без проблем
- **Масштабируемость** - поддержка нескольких аккаунтов WoW

---

**Вывод:** SavedVariables - единственный надежный способ передачи данных из WoW аддонов во внешние приложения. HTTP запросы заблокированы Blizzard по соображениям безопасности.
