# WoW to Discord - Передача сообщений из World of Warcraft в Discord

Этот проект позволяет автоматически пересылать входящие сообщения из World of Warcraft в Discord канал в реальном времени.

## 🚀 Особенности

- ✅ **Работает через SavedVariables** - надежный метод передачи данных из WoW
- ✅ **Поддержка всех типов сообщений** - Say, Yell, Whisper, Party, Raid, Guild и др.
- ✅ **Красивое оформление в Discord** - сообщения отображаются в виде embed'ов
- ✅ **Автоматический мониторинг** - сервер отслеживает изменения в файлах WoW
- ✅ **Простая настройка** - slash команды в игре
- ✅ **Мониторинг статуса** - веб-интерфейс для проверки работы
- ✅ **Обходит ограничения WoW** - не использует заблокированные HTTP запросы

## 📋 Требования

- World of Warcraft (любая версия)
- Node.js 16+ 
- Discord бот токен
- Права администратора на Discord сервере

## 🛠️ Установка

### Шаг 1: Создание Discord бота

1. Перейдите на [Discord Developer Portal](https://discord.com/developers/applications)
2. Нажмите "New Application" и дайте название боту
3. Перейдите в раздел "Bot" и нажмите "Add Bot"
4. Скопируйте токен бота (понадобится позже)
5. В разделе "OAuth2" → "URL Generator":
   - Выберите scope: `bot`
   - Выберите permissions: `Send Messages`, `Use Slash Commands`, `Embed Links`
   - Скопируйте сгенерированную ссылку и добавьте бота на свой сервер

### Шаг 2: Установка WoW аддона

1. Скопируйте папку `WoWToDiscord` в директорию аддонов WoW:
   ```
   World of Warcraft\_retail_\Interface\AddOns\WoWToDiscord\
   ```

2. Запустите WoW и включите аддон в списке аддонов

### Шаг 3: Настройка сервера

1. Установите Node.js с [официального сайта](https://nodejs.org/)

2. Перейдите в папку `discord-server` и установите зависимости:
   ```bash
   cd discord-server
   npm install
   ```

3. Создайте файл `.env` на основе `.env.example`:
   ```bash
   cp .env.example .env
   ```

4. Отредактируйте `.env` файл:
   ```env
   DISCORD_BOT_TOKEN=ваш_токен_бота
   DISCORD_CHANNEL_ID=id_канала_для_сообщений
   WOW_SAVEDVARIABLES_PATH=C:\Program Files (x86)\World of Warcraft\_retail_\WTF\Account\*\SavedVariables\WoWToDiscord.lua
   POLL_INTERVAL=3000
   PORT=3000
   ```

### Шаг 4: Получение ID канала Discord

1. Включите режим разработчика в Discord (Настройки → Расширенные → Режим разработчика)
2. Правой кнопкой мыши на канал → "Копировать ID"
3. Вставьте ID в файл `.env`

## 🚀 Запуск

1. Запустите сервер:
   ```bash
   cd discord-server
   npm start
   ```

2. Запустите WoW и войдите в игру

3. Проверьте работу командой в игре:
   ```
   /wtd status
   ```

## 🎮 Команды в игре

- `/wtd status` - показать статус аддона
- `/wtd toggle` - включить/выключить аддон  
- `/wtd test` - отправить тестовое сообщение
- `/wtd url <адрес>` - изменить адрес сервера

## 🔧 Настройка

### Настройка типов сообщений

По умолчанию перехватываются все типы сообщений. Чтобы отключить определенные типы, отредактируйте файл `WoWToDiscord.lua`:

```lua
channels = {
    SAY = true,        -- Сказать
    YELL = false,      -- Кричать (отключено)
    WHISPER = true,    -- Шепот
    PARTY = true,      -- Группа
    RAID = true,       -- Рейд
    GUILD = true,      -- Гильдия
    OFFICER = true,    -- Офицерский канал
    CHANNEL = true,    -- Каналы чата
    EMOTE = false,     -- Эмоции (отключено)
    TEXT_EMOTE = false -- Текстовые эмоции (отключено)
}
```

### Настройка сервера

В файле `.env` можно настроить:

```env
# Порт сервера
PORT=3000

# Включить файловый fallback
ENABLE_FILE_WATCHER=true

# Директория для временных файлов
WATCH_DIRECTORY=./wow_messages

# Цвет embed'ов в Discord (hex)
MESSAGE_EMBED_COLOR=0x0099ff
```

## 🔍 Диагностика проблем

### Проверка статуса сервера
Откройте в браузере: `http://localhost:3000/status`

### Тестирование без игры
```bash
curl -X POST http://localhost:3000/test
```

### Логи сервера
Сервер выводит подробные логи всех операций:
```
✅ Discord bot logged in as YourBot#1234
✅ Connected to Discord channel: general
📨 Received message from WoW: PlayerName (GUILD): Hello world!
✅ Message sent to Discord: PlayerName (GUILD)
```

### Настройка пути к SavedVariables

По умолчанию сервер ищет файлы по пути:
```
C:\Program Files (x86)\World of Warcraft\_retail_\WTF\Account\*\SavedVariables\WoWToDiscord.lua
```

Если ваш WoW установлен в другом месте, измените `WOW_SAVEDVARIABLES_PATH` в `.env` файле.

## 🛡️ Безопасность

- Никогда не публикуйте токен Discord бота
- Используйте отдельный канал для сообщений из игры
- Регулярно проверяйте логи сервера

## ⚡ Быстрый старт

1. **Создайте Discord бота** и получите токен
2. **Установите аддон** в папку WoW AddOns
3. **Запустите сервер**: `npm install && npm start`
4. **Настройте .env** с токеном, ID канала и путем к WoW
5. **Протестируйте**: `/wtd test` в игре

## 🔧 Как это работает

### SavedVariables метод (единственный рабочий)
- ✅ **100% надежность** - WoW не блокирует этот метод
- ✅ **Автоматическое обновление** - сервер отслеживает изменения файлов
- ✅ **Быстрая доставка** - проверка каждые 3 секунды
- ✅ **Без ограничений** - обходит все блокировки WoW API

**Почему HTTP не работает:** WoW блокирует HTTP запросы из аддонов по соображениям безопасности. SavedVariables - единственный надежный способ передачи данных.

## 📊 Мониторинг

### Веб-интерфейс статуса
- `http://localhost:3000/status` - статус сервера и Discord
- `http://localhost:3000/test` - отправка тестового сообщения

### Команды в игре
```
/wtd status    # Показать статус
/wtd toggle    # Вкл/выкл аддон
/wtd test      # Тест сообщение
```

## 🎯 Поддерживаемые типы сообщений

| Тип | Описание | По умолчанию |
|-----|----------|--------------|
| SAY | Обычный чат | ✅ |
| YELL | Крик | ✅ |
| WHISPER | Личные сообщения | ✅ |
| PARTY | Группа | ✅ |
| RAID | Рейд | ✅ |
| GUILD | Гильдия | ✅ |
| OFFICER | Офицерский канал | ✅ |
| CHANNEL | Каналы чата | ✅ |
| EMOTE | Эмоции | ✅ |

## 📝 Лицензия

MIT License - используйте свободно для личных и коммерческих целей.

## 🤝 Поддержка

Если возникли проблемы:
1. Проверьте логи сервера
2. Убедитесь, что бот добавлен на сервер с нужными правами
3. Проверьте правильность токена и ID канала
4. Убедитесь, что аддон включен в WoW

---

**Приятной игры! 🎮**
