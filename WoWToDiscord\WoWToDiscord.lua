-- WoW to Discord Addon v2.0
-- Перехватывает входящие сообщения и сохраняет их для передачи в Discord
-- Использует только файловый метод (HTTP запросы в WoW заблокированы)

local WoWToDiscord = {}
WoWToDiscord.version = "2.0.0"

-- Настройки по умолчанию
local defaults = {
    enabled = true,
    maxMessagesInMemory = 100,
    writeInterval = 2, -- секунд между записями
    channels = {
        SAY = true,
        YELL = true,
        WHISPER = true,
        PARTY = true,
        RAID = true,
        GUILD = true,
        OFFICER = true,
        CHANNEL = true,
        EMOTE = true,
        TEXT_EMOTE = true
    }
}

-- Переменные для работы
local messageQueue = {}
local lastWriteTime = 0
local messageCounter = 0

-- Инициализация базы данных
local function InitializeDB()
    if not WoWToDiscordDB then
        WoWToDiscordDB = {}
    end
    
    for key, value in pairs(defaults) do
        if WoWToDiscordDB[key] == nil then
            WoWToDiscordDB[key] = value
        end
    end
    
    -- Инициализируем очередь сообщений
    if not WoWToDiscordDB.messageQueue then
        WoWToDiscordDB.messageQueue = {}
    end
    
    -- Очищаем старые сообщения при загрузке
    WoWToDiscord:CleanupOldMessages()
end

-- Функция для добавления сообщения в очередь
local function QueueMessage(messageData)
    if not WoWToDiscordDB.enabled then
        return
    end
    
    messageCounter = messageCounter + 1
    
    -- Создаем запись сообщения
    local messageEntry = {
        id = messageCounter,
        message = messageData.message,
        sender = messageData.sender,
        channel = messageData.channel,
        timestamp = time(),
        realm = GetRealmName() or "Unknown",
        processed = false
    }
    
    -- Добавляем в очередь в памяти
    table.insert(messageQueue, messageEntry)
    
    -- Добавляем в SavedVariables для постоянного хранения
    table.insert(WoWToDiscordDB.messageQueue, messageEntry)
    
    -- Ограничиваем размер очереди
    if #WoWToDiscordDB.messageQueue > WoWToDiscordDB.maxMessagesInMemory then
        table.remove(WoWToDiscordDB.messageQueue, 1)
    end
    
    -- Записываем периодически
    local currentTime = time()
    if (currentTime - lastWriteTime) >= WoWToDiscordDB.writeInterval then
        WoWToDiscord:FlushMessages()
        lastWriteTime = currentTime
    end
    
    print(string.format("|cff00ffff[WoWToDiscord]|r Queued: %s (%s): %s", 
          messageData.sender, messageData.channel, messageData.message))
end

-- Функция для принудительной записи сообщений
function WoWToDiscord:FlushMessages()
    if #messageQueue == 0 then
        return
    end
    
    print(string.format("|cff00ff00[WoWToDiscord]|r Flushed %d messages to queue", #messageQueue))
    messageQueue = {}
end

-- Функция для очистки старых сообщений
function WoWToDiscord:CleanupOldMessages()
    if not WoWToDiscordDB.messageQueue then
        return
    end
    
    local currentTime = time()
    local cleanupThreshold = 3600 -- 1 час
    local cleaned = 0
    
    for i = #WoWToDiscordDB.messageQueue, 1, -1 do
        local msg = WoWToDiscordDB.messageQueue[i]
        if msg.processed or (currentTime - msg.timestamp) > cleanupThreshold then
            table.remove(WoWToDiscordDB.messageQueue, i)
            cleaned = cleaned + 1
        end
    end
    
    if cleaned > 0 then
        print(string.format("|cff00ff00[WoWToDiscord]|r Cleaned up %d old messages", cleaned))
    end
end

-- Функция для получения непрочитанных сообщений (для сервера)
function WoWToDiscord:GetUnprocessedMessages()
    if not WoWToDiscordDB.messageQueue then
        return {}
    end
    
    local unprocessed = {}
    for _, msg in ipairs(WoWToDiscordDB.messageQueue) do
        if not msg.processed then
            table.insert(unprocessed, msg)
        end
    end
    
    return unprocessed
end

-- Функция для отметки сообщений как обработанных
function WoWToDiscord:MarkMessagesProcessed(messageIds)
    if not WoWToDiscordDB.messageQueue or not messageIds then
        return
    end
    
    local marked = 0
    for _, msg in ipairs(WoWToDiscordDB.messageQueue) do
        for _, id in ipairs(messageIds) do
            if msg.id == id then
                msg.processed = true
                marked = marked + 1
                break
            end
        end
    end
    
    if marked > 0 then
        print(string.format("|cff00ff00[WoWToDiscord]|r Marked %d messages as processed", marked))
    end
end

-- Обработчик входящих сообщений
local function OnChatMessage(self, event, message, sender, ...)
    local channel = event:gsub("CHAT_MSG_", "")
    
    -- Проверяем, нужно ли обрабатывать этот тип канала
    if not WoWToDiscordDB.channels[channel] then
        return
    end
    
    -- Игнорируем собственные сообщения
    local playerName = UnitName("player")
    if sender == playerName then
        return
    end
    
    -- Подготавливаем данные сообщения
    local messageData = {
        message = message,
        sender = sender,
        channel = channel
    }
    
    -- Добавляем в очередь
    QueueMessage(messageData)
end

-- Создаем фрейм для обработки событий
local frame = CreateFrame("Frame")

-- Регистрируем события для всех типов сообщений
local chatEvents = {
    "CHAT_MSG_SAY",
    "CHAT_MSG_YELL", 
    "CHAT_MSG_WHISPER",
    "CHAT_MSG_PARTY",
    "CHAT_MSG_RAID",
    "CHAT_MSG_GUILD",
    "CHAT_MSG_OFFICER",
    "CHAT_MSG_CHANNEL",
    "CHAT_MSG_EMOTE",
    "CHAT_MSG_TEXT_EMOTE"
}

for _, event in ipairs(chatEvents) do
    frame:RegisterEvent(event)
end

frame:RegisterEvent("ADDON_LOADED")
frame:RegisterEvent("PLAYER_LOGIN")
frame:RegisterEvent("PLAYER_LOGOUT")

-- Обработчик событий
frame:SetScript("OnEvent", function(self, event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        if addonName == "WoWToDiscord" then
            InitializeDB()
            print("|cff00ff00[WoWToDiscord]|r v" .. WoWToDiscord.version .. " loaded!")
            print("|cff00ff00[WoWToDiscord]|r Using file-based message transfer")
        end
    elseif event == "PLAYER_LOGIN" then
        print("|cff00ff00[WoWToDiscord]|r Ready to capture messages!")
        -- Очищаем старые сообщения при входе
        C_Timer.After(5, function()
            WoWToDiscord:CleanupOldMessages()
        end)
    elseif event == "PLAYER_LOGOUT" then
        -- Принудительно записываем все сообщения перед выходом
        WoWToDiscord:FlushMessages()
    else
        -- Обрабатываем сообщения чата
        OnChatMessage(self, event, ...)
    end
end)

-- Таймер для периодической очистки и записи
local cleanupTimer = C_Timer.NewTicker(30, function()
    WoWToDiscord:CleanupOldMessages()
    WoWToDiscord:FlushMessages()
end)

-- Slash команды для управления аддоном
SLASH_WOWTODISCORD1 = "/wtd"
SLASH_WOWTODISCORD2 = "/wowtodiscord"

SlashCmdList["WOWTODISCORD"] = function(msg)
    local command, arg = msg:match("^(%S*)%s*(.-)$")
    command = command:lower()
    
    if command == "toggle" then
        WoWToDiscordDB.enabled = not WoWToDiscordDB.enabled
        print("|cff00ff00[WoWToDiscord]|r " .. (WoWToDiscordDB.enabled and "Enabled" or "Disabled"))
    elseif command == "status" then
        print("|cff00ff00[WoWToDiscord]|r Status: " .. (WoWToDiscordDB.enabled and "Enabled" or "Disabled"))
        print("|cff00ff00[WoWToDiscord]|r Queued messages: " .. #WoWToDiscordDB.messageQueue)
        local unprocessed = WoWToDiscord:GetUnprocessedMessages()
        print("|cff00ff00[WoWToDiscord]|r Unprocessed: " .. #unprocessed)
    elseif command == "test" then
        QueueMessage({
            message = "Test message from WoW addon v" .. WoWToDiscord.version,
            sender = UnitName("player"),
            channel = "TEST"
        })
        print("|cff00ff00[WoWToDiscord]|r Test message queued")
    elseif command == "flush" then
        WoWToDiscord:FlushMessages()
        print("|cff00ff00[WoWToDiscord]|r Messages flushed")
    elseif command == "cleanup" then
        WoWToDiscord:CleanupOldMessages()
        print("|cff00ff00[WoWToDiscord]|r Cleanup completed")
    elseif command == "queue" then
        local unprocessed = WoWToDiscord:GetUnprocessedMessages()
        print("|cff00ff00[WoWToDiscord]|r Message queue:")
        for i, msg in ipairs(unprocessed) do
            if i <= 5 then -- показываем только первые 5
                print(string.format("  %d. %s (%s): %s", msg.id, msg.sender, msg.channel, 
                      string.sub(msg.message, 1, 50) .. (string.len(msg.message) > 50 and "..." or "")))
            end
        end
        if #unprocessed > 5 then
            print(string.format("  ... and %d more", #unprocessed - 5))
        end
    else
        print("|cff00ff00[WoWToDiscord]|r Commands:")
        print("  /wtd toggle - Enable/disable addon")
        print("  /wtd status - Show current status")
        print("  /wtd test - Add test message to queue")
        print("  /wtd flush - Force flush messages")
        print("  /wtd cleanup - Clean old messages")
        print("  /wtd queue - Show message queue")
    end
end

-- Экспортируем для глобального доступа
_G.WoWToDiscord = WoWToDiscord
